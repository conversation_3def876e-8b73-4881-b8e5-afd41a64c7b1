<script setup lang="ts">
import type { EasyCurdConfig } from '@billing/curd'

import type { ApiKey } from '@/api'
import { EasyCurd } from '@billing/curd'
import {
  Alert,
  AlertDescription,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Badge,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@billing/ui'
import { Activity, CheckCheck, Copy, Eye, Info, Key, Package, ShieldCheck, ShieldX, Wallet } from 'lucide-vue-next'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { keyApi, searchUsers } from '@/api'

const router = useRouter()
const activeTab = ref('list')

// 统计数据
const stats = ref({
  totalKeys: 0,
  activeKeys: 0,
  blockedKeys: 0,
  keysWithQuota: 0,
  keysWithBalance: 0,
  totalQuotaUsage: 0,
  avgQuotaUsage: 0,
})

// 获取统计数据
async function fetchStats() {
  try {
    // 这里应该调用实际的API获取统计数据
    // const response = await keyApi.getStats()
    // stats.value = response

    // 模拟数据
    stats.value = {
      totalKeys: 156,
      activeKeys: 142,
      blockedKeys: 14,
      keysWithQuota: 128,
      keysWithBalance: 89,
      totalQuotaUsage: 68.5,
      avgQuotaUsage: 45.2,
    }
  }
  catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// EasyCurd 配置
const config: EasyCurdConfig<ApiKey> = {
  api: keyApi, // 临时使用 any 类型避免类型冲突
  title: 'API Key',

  // 表单字段配置
  formFields: [
    {
      key: 'user_id',
      label: '关联用户',
      type: 'combobox',
      required: true,
      placeholder: '请输入用户名或邮箱搜索',
      remoteSearch: {
        searchApi: searchUsers,
        debounceMs: 300,
        minSearchLength: 1,
        showDefaultOptions: false,
        renderOption: (option) => {
          const parts = [option.label]
          if (option.email) {
            parts.push(`(${option.email})`)
          }
          return parts.join(' ')
        },
      },
    },
    {
      key: 'name',
      label: 'Key名称',
      type: 'text',
      placeholder: '请输入Key名称',
      required: true,
    },
    {
      key: 'api_key',
      label: 'API Key',
      type: 'text',
      show: false,
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      defaultValue: 'ok',
      options: [
        { label: '正常', value: 'ok' },
        { label: '阻止', value: 'blocked' },
      ],
      required: true,
    },
    {
      key: 'comment',
      label: '备注',
      type: 'textarea',
      placeholder: '请输入Key备注信息',
    },
  ],

  // 基础配置
  primaryKey: 'id',
  pageSize: 20,
  pageSizeOptions: [10, 20, 50, 100],

  // 功能开关
  features: {
    create: true,
    edit: true,
    delete: true,
    batchDelete: true,
    search: true,
  },

  // 消息配置
  confirmMessages: {
    delete: '确定要删除这个API Key吗？删除后无法恢复！',
    batchDelete: '确定要删除选中的API Key吗？此操作不可撤销！',
  },

  successMessages: {
    create: 'API Key创建成功！',
    update: 'API Key更新成功！',
    delete: 'API Key删除成功！',
    batchDelete: '批量删除完成！',
  },
}

// 模块统计数据
const moduleStats = computed(() => {
  return [
    { module: 'llm', name: 'LLM服务', keyCount: 89, activeCount: 82, avgUsage: 56.7 },
    { module: 'tts', name: 'TTS服务', keyCount: 45, activeCount: 41, avgUsage: 32.1 },
    { module: 'asr', name: 'ASR服务', keyCount: 28, activeCount: 25, avgUsage: 28.9 },
  ]
})

const isCopiedMap = ref<Record<string, boolean>>({})

function copyApiKey(key: string) {
  if (!key)
    return
  navigator.clipboard.writeText(key)
  isCopiedMap.value[key] = true
  setTimeout(() => {
    isCopiedMap.value[key] = false
  }, 5000)
}

// 遮蔽 API Key
function maskApiKey(key: string) {
  if (!key)
    return ''
  return `${key.substring(0, 4)}****${key.substring(key.length - 4)}`
}

// 查看Key详情
function viewKey(row: ApiKey) {
  router.push(`/billing/keys/${row.api_key}`)
}

// 格式化货币
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
  }).format(amount)
}

// 格式化时间
function formatDate(dateString: string) {
  if (!dateString)
    return '从未'

  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(dateString))
}

// 初始化
onMounted(() => {
  fetchStats()
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          API Key 管理
        </h1>
        <p class="mt-2 text-sm text-gray-700">
          管理系统中的所有API Key，包括查看、创建、禁用等操作。支持资源包和余额两种计费方式。
        </p>
      </div>
    </div>

    <!-- 计费方式说明 -->
    <Alert>
      <Info class="w-4 h-4" />
      <AlertDescription>
        <strong>计费说明：</strong>系统支持两种计费方式：①资源包计费（优先）- 从配置的资源包中扣减用量；②余额计费（兜底）- 当资源包不足时，从用户余额中扣费。
      </AlertDescription>
    </Alert>

    <!-- 标签页导航 -->
    <Tabs
      v-model="activeTab"
      class="w-full"
    >
      <TabsList class="grid w-full grid-cols-2">
        <TabsTrigger value="overview">
          概览
        </TabsTrigger>
        <TabsTrigger value="list">
          API Key列表
        </TabsTrigger>
      </TabsList>

      <!-- 概览标签页 -->
      <TabsContent
        value="overview"
        class="space-y-6"
      >
        <!-- 核心统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                总Key数量
              </CardTitle>
              <Key class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ stats.totalKeys }}
              </div>
              <p class="text-xs text-muted-foreground">
                活跃: {{ stats.activeKeys }}，阻止: {{ stats.blockedKeys }}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                配额覆盖率
              </CardTitle>
              <Package class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ Math.round((stats.keysWithQuota / stats.totalKeys) * 100) }}%
              </div>
              <p class="text-xs text-muted-foreground">
                {{ stats.keysWithQuota }}/{{ stats.totalKeys }} 个Key有配额
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                平均使用率
              </CardTitle>
              <Activity class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ stats.avgQuotaUsage }}%
              </div>
              <p class="text-xs text-muted-foreground">
                配额平均使用率
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                余额覆盖率
              </CardTitle>
              <Wallet class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ Math.round((stats.keysWithBalance / stats.totalKeys) * 100) }}%
              </div>
              <p class="text-xs text-muted-foreground">
                {{ stats.keysWithBalance }}/{{ stats.totalKeys }} 个Key用户有余额
              </p>
            </CardContent>
          </Card>
        </div>

        <!-- 按服务类型统计 -->
        <Card>
          <CardHeader>
            <CardTitle>按服务类型统计</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div
                v-for="module in moduleStats"
                :key="module.module"
                class="flex items-center justify-between p-4 border rounded-lg"
              >
                <div class="flex items-center gap-3">
                  <Badge variant="outline">
                    {{ module.name }}
                  </Badge>
                  <div class="text-sm text-gray-600">
                    {{ module.keyCount }} 个Key使用
                  </div>
                </div>
                <div class="text-right space-y-1">
                  <div class="font-medium">
                    活跃: {{ module.activeCount }}/{{ module.keyCount }}
                  </div>
                  <div class="text-sm text-gray-500">
                    平均使用率: {{ module.avgUsage }}%
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- API Key列表标签页 -->
      <TabsContent
        value="list"
        class="space-y-6"
      >
        <!-- 使用 EasyCurd 组件 -->
        <EasyCurd :config="config">
          <!-- 自定义API Key列渲染 -->
          <template #cell-api_key="{ value, row }">
            <div class="flex items-center gap-3">
              <div class="flex-shrink-0">
                <Key class="w-5 h-5 text-gray-400" />
              </div>
              <div>
                <div class="flex items-center justify-between font-mono text-sm font-medium text-gray-900">
                  {{ maskApiKey(value) }}
                  <CheckCheck
                    v-if="isCopiedMap[value]"
                    class="w-4 h-4 ml-2 text-success cursor-pointer"
                    @click="copyApiKey(value)"
                  />
                  <Copy
                    v-else
                    class="w-4 h-4 ml-2 cursor-pointer"
                    @click="copyApiKey(value)"
                  />
                </div>
                <div
                  v-if="row.name"
                  class="text-xs text-gray-500"
                >
                  {{ row.name }}
                </div>
              </div>
            </div>
          </template>

          <!-- 自定义关联用户列渲染 -->
          <template #cell-user_id="{ row }">
            <div
              v-if="row.user"
              class="flex items-center"
            >
              <Avatar class="h-8 w-8 mr-3">
                <AvatarImage :src="row.user.avatar" />
                <AvatarFallback>{{ (row.user.name || '').charAt(0).toUpperCase() }}</AvatarFallback>
              </Avatar>
              <div>
                <div class="text-sm font-medium text-gray-900">
                  {{ row.user.name }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ row.user.email }}
                </div>
                <div
                  v-if="row.user.balance"
                  class="text-xs text-green-600"
                >
                  余额: {{ formatCurrency(row.user.balance) }}
                </div>
              </div>
            </div>
            <div
              v-else
              class="text-sm text-gray-400"
            >
              无关联用户
            </div>
          </template>

          <!-- 自定义状态列渲染 -->
          <template #cell-status="{ value }">
            <Badge :variant="value === 'ok' ? 'default' : 'destructive'">
              <div class="flex items-center gap-1">
                <div
                  class="w-2 h-2 rounded-full"
                  :class="value === 'ok' ? 'bg-green-500' : 'bg-red-500'"
                />
                {{ value === 'ok' ? '正常' : '阻止' }}
              </div>
            </Badge>
          </template>

          <!-- 自定义创建时间列渲染 -->
          <template #cell-createdAt="{ value }">
            <div class="text-sm text-gray-500">
              {{ formatDate(value) }}
            </div>
          </template>

          <!-- 自定义操作列 -->
          <template #actions="{ row }">
            <div class="flex items-center justify-end gap-1">
              <Button
                variant="ghost"
                size="sm"
                @click="viewKey(row)"
              >
                <Eye class="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
              >
                <component
                  :is="row.status === 'ok' ? ShieldCheck : ShieldX"
                  class="w-4 h-4"
                />
              </Button>
            </div>
          </template>
        </EasyCurd>
      </TabsContent>
    </Tabs>
  </div>
</template>
