import type { ListResponse } from '@billing/curd'
import type { ModelBase } from './http'
import type { QuotaPackage } from './quota'
import type { User } from './user'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

// 定义API Key接口类型
export interface ApiKey extends ModelBase {
  api_key: string
  name?: string
  module: 'llm' | 'tts' | 'asr'
  user_id: string
  status: 'ok' | 'blocked'
  comment?: string
  llm_quotas?: QuotaPackage[]
  tts_quotas?: QuotaPackage[]
  asr_quotas?: QuotaPackage[]
  user?: User
}

// Key状态响应
export interface KeyStatusResponse {
  key: string
  status: 'ok' | 'blocked'
  available: boolean
  quotas: QuotaPackage[]
  user?: User
  created_at: number
  updated_at: number
}

// 用量历史
export interface UsageLog {
  id: string
  api_key_id: string
  api_key: string
  module: 'llm' | 'tts' | 'asr'
  model_name?: string
  usage: number
  unit_price: number
  cost: number
  currency: 'CNY' | 'USD'
  unit: string
  billing_type: 'quota' | 'balance'
  quota_package_id?: string
  user_id?: string
  metadata?: Record<string, any>
  created_at: number
  updated_at: number
}

// 用量统计
export interface UsageStats {
  period: 'day' | 'week' | 'month'
  start_time: number
  end_time: number
  total_cost: number // 总成本（可以相加）
  total_count: number // 总请求数（可以相加）
  by_module: ModuleStats[] // 按模块分开的统计
}

export interface ModuleStats {
  module: string
  total_usage: number // 改名为usage，因为不同模块单位不同
  total_cost: number
  count: number
  unit: string // 添加单位字段，如 'tokens', 'characters', 'seconds'
}

// 更新配额请求
export interface UpdateQuotaRequest {
  module: 'llm' | 'tts' | 'asr'
  quota: number
  expires_at?: number
  description: string
}

// 创建配额请求
export interface CreateQuotaRequest {
  module: 'llm' | 'tts' | 'asr'
  quota: number
  expires_at?: number
  model_name?: string
  description?: string
}

export const keyApi = extendCurdApi(useCurdApi<ApiKey>('/admin/billing/keys'), {
  // 获取Key状态和详细信息
  getKeyStatus: async (key: string): Promise<KeyStatusResponse> => {
    return await http.get(`/admin/billing/keys/${key}/status`)
  },

  // 获取用量历史
  getUsageHistory: async (key: string, params?: {
    page?: number
    page_size?: number
    module?: string
    start_time?: number
    end_time?: number
  }): Promise<ListResponse<UsageLog>> => {
    return await http.get(`/admin/billing/keys/${key}/usage`, {
      params,
    })
  },

  // 获取用量统计
  getUsageStats: async (key: string, period: 'day' | 'week' | 'month' = 'month'): Promise<UsageStats> => {
    return await http.get(`/admin/billing/keys/${key}/stats`, {
      params: {
        period,
      },
    })
  },
})
