package model

import (
	"testing"
)

func TestPricingRule_CalculateCost(t *testing.T) {
	tests := []struct {
		name      string
		rule      *PricingRule
		usage     int64
		expected  float64
	}{
		{
			name: "基础计费 - 1:1",
			rule: &PricingRule{
				UnitPrice: 0.001,
				BaseUnit:  1,
			},
			usage:    1000,
			expected: 1.0, // 1000 * 0.001
		},
		{
			name: "千单位计费",
			rule: &PricingRule{
				UnitPrice: 2.0,   // 2元/千tokens
				BaseUnit:  1000,
			},
			usage:    5000,
			expected: 10.0, // (5000/1000) * 2.0 = 5 * 2.0 = 10.0
		},
		{
			name: "百万单位计费",
			rule: &PricingRule{
				UnitPrice: 10.0,     // 10元/百万tokens
				BaseUnit:  1000000,
			},
			usage:    2500000,
			expected: 25.0, // (2500000/1000000) * 10.0 = 2.5 * 10.0 = 25.0
		},
		{
			name: "万单位计费",
			rule: &PricingRule{
				UnitPrice: 5.0,   // 5元/万tokens
				BaseUnit:  10000,
			},
			usage:    30000,
			expected: 15.0, // (30000/10000) * 5.0 = 3 * 5.0 = 15.0
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.rule.CalculateCost(tt.usage)
			if result != tt.expected {
				t.Errorf("CalculateCost() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestPricingRule_GetEffectiveUnitPrice(t *testing.T) {
	tests := []struct {
		name      string
		rule      *PricingRule
		expected  float64
	}{
		{
			name: "基础单价 - 1:1",
			rule: &PricingRule{
				UnitPrice: 0.001,
				BaseUnit:  1,
			},
			expected: 0.001, // 0.001 / 1
		},
		{
			name: "千单位单价",
			rule: &PricingRule{
				UnitPrice: 2.0,   // 2元/千tokens
				BaseUnit:  1000,
			},
			expected: 0.002, // 2.0 / 1000 = 0.002元/token
		},
		{
			name: "百万单位单价",
			rule: &PricingRule{
				UnitPrice: 10.0,     // 10元/百万tokens
				BaseUnit:  1000000,
			},
			expected: 0.00001, // 10.0 / 1000000 = 0.00001元/token
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.rule.GetEffectiveUnitPrice()
			if result != tt.expected {
				t.Errorf("GetEffectiveUnitPrice() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestPricingRule_GetUnitDisplay(t *testing.T) {
	tests := []struct {
		name     string
		rule     *PricingRule
		expected string
	}{
		{
			name: "基础单位",
			rule: &PricingRule{
				Unit:     "token",
				BaseUnit: 1,
			},
			expected: "token",
		},
		{
			name: "千单位",
			rule: &PricingRule{
				Unit:     "token",
				BaseUnit: 1000,
			},
			expected: "1千token",
		},
		{
			name: "万单位",
			rule: &PricingRule{
				Unit:     "token",
				BaseUnit: 10000,
			},
			expected: "1万token",
		},
		{
			name: "百万单位",
			rule: &PricingRule{
				Unit:     "token",
				BaseUnit: 1000000,
			},
			expected: "1百万token",
		},
		{
			name: "2千单位",
			rule: &PricingRule{
				Unit:     "request",
				BaseUnit: 2000,
			},
			expected: "2千request",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.rule.GetUnitDisplay()
			if result != tt.expected {
				t.Errorf("GetUnitDisplay() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestPricingRule_GetDisplayPrice(t *testing.T) {
	tests := []struct {
		name     string
		rule     *PricingRule
		expected string
	}{
		{
			name: "人民币基础单位",
			rule: &PricingRule{
				UnitPrice: 0.001,
				Currency:  "CNY",
				Unit:      "token",
				BaseUnit:  1,
			},
			expected: "¥0.001000/token",
		},
		{
			name: "美元千单位",
			rule: &PricingRule{
				UnitPrice: 2.0,
				Currency:  "USD",
				Unit:      "token",
				BaseUnit:  1000,
			},
			expected: "$2.000000/1千token",
		},
		{
			name: "人民币百万单位",
			rule: &PricingRule{
				UnitPrice: 10.0,
				Currency:  "CNY",
				Unit:      "token",
				BaseUnit:  1000000,
			},
			expected: "¥10.000000/1百万token",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.rule.GetDisplayPrice()
			if result != tt.expected {
				t.Errorf("GetDisplayPrice() = %v, expected %v", result, tt.expected)
			}
		})
	}
}
